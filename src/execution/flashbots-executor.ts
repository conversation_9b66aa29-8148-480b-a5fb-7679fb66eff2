import { ethers } from 'ethers';
import { FlashbotsBundleTransaction } from '@flashbots/ethers-provider-bundle';
import { FlashbotsBundleManager } from '../flashbots/bundle-provider';
import { AdvancedGasEstimator } from '../gas/advanced-estimator';
import { GasOptimizer } from '../gas/optimizer';
import { config } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';
import { ArbitrageRoute, FlashloanRoute } from '../types';

export interface ExecutionResult {
  success: boolean;
  txHash?: string;
  bundleHash?: string;
  gasUsed?: bigint;
  gasPrice?: bigint;
  profit?: bigint;
  error?: string;
  executionTime?: number;
}

export interface ExecutionOptions {
  useFlashbots: boolean;
  urgency: 'slow' | 'standard' | 'fast' | 'instant';
  maxGasCostEth: number;
  slippageTolerance: number;
  deadline?: number;
}

/**
 * Enhanced MEV Executor with Flashbots integration
 * Handles both regular mempool and Flashbots bundle execution
 */
export class FlashbotsExecutor {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsManager: FlashbotsBundleManager;
  private gasEstimator: AdvancedGasEstimator;
  private gasOptimizer: GasOptimizer;

  constructor(
    provider: ethers.JsonRpcProvider,
    wallet: ethers.Wallet,
    flashbotsManager: FlashbotsBundleManager,
    gasEstimator: AdvancedGasEstimator,
    gasOptimizer: GasOptimizer
  ) {
    this.provider = provider;
    this.wallet = wallet;
    this.flashbotsManager = flashbotsManager;
    this.gasEstimator = gasEstimator;
    this.gasOptimizer = gasOptimizer;
  }

  /**
   * Execute arbitrage opportunity
   */
  async executeArbitrage(
    route: ArbitrageRoute,
    options: ExecutionOptions = {
      useFlashbots: true,
      urgency: 'fast',
      maxGasCostEth: 0.01,
      slippageTolerance: 0.5
    }
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      enhancedLogger.systemStatus('🚀 Executing arbitrage opportunity...');
      enhancedLogger.systemStatus(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
      enhancedLogger.systemStatus(`   Use Flashbots: ${options.useFlashbots}`);

      // Check if gas conditions are favorable
      const gasFavorable = await this.gasEstimator.isGasFavorable(options.maxGasCostEth);
      if (!gasFavorable) {
        enhancedLogger.systemStatus('⚠️  Gas prices too high, skipping execution');
        return { success: false, error: 'Gas prices unfavorable' };
      }

      // Build transaction
      const transaction = await this.buildArbitrageTransaction(route, options);
      if (!transaction) {
        return { success: false, error: 'Failed to build transaction' };
      }

      // Execute based on strategy
      if (options.useFlashbots && this.flashbotsManager.isAvailable()) {
        return await this.executeViaFlashbots([transaction], options);
      } else {
        return await this.executeViaMempool(transaction, options);
      }

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeArbitrage');
      return {
        success: false,
        error: (error as Error).message,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Execute flashloan opportunity
   */
  async executeFlashloan(
    route: FlashloanRoute,
    options: ExecutionOptions = {
      useFlashbots: true,
      urgency: 'fast',
      maxGasCostEth: 0.02,
      slippageTolerance: 0.3
    }
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      enhancedLogger.systemStatus('💰 Executing flashloan opportunity...');
      enhancedLogger.systemStatus(`   Flashloan Amount: ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
      enhancedLogger.systemStatus(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);

      // Check profitability after current gas costs
      const gasEstimate = await this.gasEstimator.calculateGasCost(
        BigInt(500000), // Estimated gas for flashloan
        options.urgency
      );

      if (!this.gasOptimizer.isProfitable(route.expectedProfit, gasEstimate)) {
        enhancedLogger.systemStatus('⚠️  Opportunity no longer profitable after gas costs');
        return { success: false, error: 'Not profitable after gas costs' };
      }

      // Build flashloan transaction
      const transaction = await this.buildFlashloanTransaction(route, options);
      if (!transaction) {
        return { success: false, error: 'Failed to build flashloan transaction' };
      }

      // Execute based on strategy
      if (options.useFlashbots && this.flashbotsManager.isAvailable()) {
        return await this.executeViaFlashbots([transaction], options);
      } else {
        return await this.executeViaMempool(transaction, options);
      }

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeFlashloan');
      return {
        success: false,
        error: (error as Error).message,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Execute transaction via Flashbots bundle
   */
  private async executeViaFlashbots(
    transactions: FlashbotsBundleTransaction[],
    options: ExecutionOptions
  ): Promise<ExecutionResult> {
    try {
      enhancedLogger.systemStatus('📦 Executing via Flashbots bundle...');

      const targetBlock = await this.flashbotsManager.getNextBlock();
      
      // Simulate bundle first
      const simulation = await this.flashbotsManager.simulateBundle(transactions, targetBlock);
      if (!simulation.success) {
        enhancedLogger.systemStatus('❌ Bundle simulation failed');
        return { success: false, error: simulation.error };
      }

      enhancedLogger.systemStatus('✅ Bundle simulation successful');

      // Submit bundle
      const submission = await this.flashbotsManager.submitBundle(transactions, targetBlock);
      if (!submission.success) {
        enhancedLogger.systemStatus('❌ Bundle submission failed');
        return { success: false, error: submission.error };
      }

      enhancedLogger.systemStatus('🎉 Bundle submitted successfully!');
      
      return {
        success: true,
        bundleHash: submission.bundleHash,
        executionTime: Date.now() - Date.now()
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeViaFlashbots');
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Execute transaction via regular mempool
   */
  private async executeViaMempool(
    transaction: ethers.TransactionRequest,
    options: ExecutionOptions
  ): Promise<ExecutionResult> {
    try {
      enhancedLogger.systemStatus('🌐 Executing via mempool...');

      // Get optimal gas pricing
      const gasPrice = await this.gasEstimator.getOptimalGasPrice(options.urgency);
      
      // Prepare transaction
      const txRequest: ethers.TransactionRequest = {
        ...transaction,
        gasPrice: gasPrice.gasPrice,
        maxFeePerGas: gasPrice.maxFeePerGas,
        maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas
      };

      if (config.dryRun) {
        enhancedLogger.systemStatus('🧪 DRY RUN: Transaction would be sent');
        enhancedLogger.systemStatus(`   Gas Price: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
        return { success: true, txHash: 'DRY_RUN_TX_HASH' };
      }

      // Send transaction
      const tx = await this.wallet.sendTransaction(txRequest);
      enhancedLogger.systemStatus(`📤 Transaction sent: ${tx.hash}`);

      // Wait for confirmation
      const receipt = await tx.wait();
      if (!receipt) {
        return { success: false, error: 'Transaction failed' };
      }

      enhancedLogger.systemStatus('✅ Transaction confirmed!');
      enhancedLogger.systemStatus(`   Gas Used: ${receipt.gasUsed}`);
      enhancedLogger.systemStatus(`   Gas Price: ${ethers.formatUnits(receipt.gasPrice || 0, 'gwei')} gwei`);

      return {
        success: true,
        txHash: receipt.hash,
        gasUsed: receipt.gasUsed,
        gasPrice: receipt.gasPrice || BigInt(0)
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeViaMempool');
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Build arbitrage transaction
   */
  private async buildArbitrageTransaction(
    route: ArbitrageRoute,
    options: ExecutionOptions
  ): Promise<FlashbotsBundleTransaction | null> {
    try {
      // This would build the actual arbitrage transaction
      // For now, return a placeholder
      const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
        route.pools[0].address,
        '0x', // Placeholder data
        0
      );

      const transaction: ethers.TransactionRequest = {
        to: route.pools[0].address,
        data: '0x', // Placeholder - would contain actual swap data
        value: 0,
        gasLimit
      };

      return this.flashbotsManager.createBundleTransaction(transaction, this.wallet);

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.buildArbitrageTransaction');
      return null;
    }
  }

  /**
   * Build flashloan transaction
   */
  private async buildFlashloanTransaction(
    route: FlashloanRoute,
    options: ExecutionOptions
  ): Promise<FlashbotsBundleTransaction | null> {
    try {
      // This would build the actual flashloan transaction
      // For now, return a placeholder
      const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
        config.hybridFlashloanContract,
        '0x', // Placeholder data
        0
      );

      const transaction: ethers.TransactionRequest = {
        to: config.hybridFlashloanContract,
        data: '0x', // Placeholder - would contain actual flashloan data
        value: 0,
        gasLimit
      };

      return this.flashbotsManager.createBundleTransaction(transaction, this.wallet);

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.buildFlashloanTransaction');
      return null;
    }
  }

  /**
   * Check if execution conditions are favorable
   */
  async isExecutionFavorable(options: ExecutionOptions): Promise<boolean> {
    try {
      // Check gas conditions
      const gasFavorable = await this.gasEstimator.isGasFavorable(options.maxGasCostEth);
      
      // Check network congestion
      const currentBlock = await this.provider.getBlockNumber();
      const block = await this.provider.getBlock(currentBlock);
      const gasUsageRatio = Number(block?.gasUsed || 0) / Number(block?.gasLimit || 1);
      
      // Avoid execution during high congestion (>90% block utilization)
      const congestionOk = gasUsageRatio < 0.9;

      enhancedLogger.systemStatus(`⛽ Gas Favorable: ${gasFavorable}`);
      enhancedLogger.systemStatus(`🚦 Network Congestion: ${(gasUsageRatio * 100).toFixed(1)}%`);

      return gasFavorable && congestionOk;

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.isExecutionFavorable');
      return false;
    }
  }

  /**
   * Get execution statistics
   */
  async getExecutionStats(): Promise<{
    flashbotsAvailable: boolean;
    gasEstimates: any;
    networkCongestion: number;
    recommendedStrategy: 'flashbots' | 'mempool';
  }> {
    try {
      const gasEstimates = await this.gasEstimator.getGasEstimates();
      const currentBlock = await this.provider.getBlockNumber();
      const block = await this.provider.getBlock(currentBlock);
      const networkCongestion = Number(block?.gasUsed || 0) / Number(block?.gasLimit || 1);

      const recommendedStrategy = this.flashbotsManager.isAvailable() && networkCongestion > 0.7 
        ? 'flashbots' 
        : 'mempool';

      return {
        flashbotsAvailable: this.flashbotsManager.isAvailable(),
        gasEstimates,
        networkCongestion,
        recommendedStrategy
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.getExecutionStats');
      return {
        flashbotsAvailable: false,
        gasEstimates: null,
        networkCongestion: 0,
        recommendedStrategy: 'mempool'
      };
    }
  }
}
