import dotenv from 'dotenv';
import { Config } from '../types';

dotenv.config();

export const config: Config = {
  rpcUrl: process.env.RPC_URL || 'https://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY',
  flashbotsRpcUrl: process.env.FLASHBOTS_RPC_URL || 'https://relay.flashbots.net',
  chainId: parseInt(process.env.CHAIN_ID || '11155111'), // Default to Sepolia
  privateKey: process.env.PRIVATE_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  flashbotsSignerKey: process.env.FLASHBOTS_SIGNER_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  minProfitWei: process.env.MIN_PROFIT_WEI || '1000000000000000000',
  maxGasPriceGwei: parseInt(process.env.MAX_GAS_PRICE_GWEI || '100'),
  maxPriorityFeeGwei: parseInt(process.env.MAX_PRIORITY_FEE_GWEI || '5'),
  slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.005'),
  mempoolWebsocketUrl: process.env.MEMPOOL_WEBSOCKET_URL || 'wss://eth-sepolia.g.alchemy.com/v2/YOUR_ALCHEMY_KEY',
  enableFlashbotsMempool: process.env.ENABLE_FLASHBOTS_MEMPOOL === 'true',
  enableEthersMempool: process.env.ENABLE_ETHERS_MEMPOOL === 'true',
  enableSandwichAttacks: process.env.ENABLE_SANDWICH_ATTACKS === 'true',
  enableFrontRunning: process.env.ENABLE_FRONT_RUNNING === 'true',
  enableArbitrage: process.env.ENABLE_ARBITRAGE === 'true',
  enableFlashloanAttacks: process.env.ENABLE_FLASHLOAN_ATTACKS === 'true',
  enableMultiBlockAttacks: process.env.ENABLE_MULTI_BLOCK_ATTACKS === 'true',
  maxBlocksAhead: parseInt(process.env.MAX_BLOCKS_AHEAD || '3'),
  maxPositionSizeEth: parseInt(process.env.MAX_POSITION_SIZE_ETH || '10'),
  emergencyStop: process.env.EMERGENCY_STOP === 'true',
  dryRun: process.env.DRY_RUN !== 'false',
  logLevel: process.env.LOG_LEVEL || 'info',
  logToFile: process.env.LOG_TO_FILE === 'true',
  // Flashloan contract addresses
  hybridFlashloanContract: process.env.HYBRID_FLASHLOAN_CONTRACT || '',
  balancerFlashloanContract: process.env.BALANCER_FLASHLOAN_CONTRACT || '',
  aaveFlashloanContract: process.env.AAVE_FLASHLOAN_CONTRACT || '',
  // Flashloan DEX configuration
  flashloanDexPairs: (process.env.FLASHLOAN_DEX_PAIRS || 'UNISWAP_V2,UNISWAP_V3').split(','),
  flashloanBuyDex: process.env.FLASHLOAN_BUY_DEX || 'UNISWAP_V2',
  flashloanSellDex: process.env.FLASHLOAN_SELL_DEX || 'UNISWAP_V3',
  enableCrossDexArbitrage: process.env.ENABLE_CROSS_DEX_ARBITRAGE === 'true',
  minArbitrageSpread: parseFloat(process.env.MIN_ARBITRAGE_SPREAD || '0.5'),
  // Flashloan token configuration
  flashloanTokens: (process.env.FLASHLOAN_TOKENS || 'USDC,WETH,USDT,DAI').split(','),
  flashloanPrimaryToken: process.env.FLASHLOAN_PRIMARY_TOKEN || 'USDC',
  flashloanTargetTokens: (process.env.FLASHLOAN_TARGET_TOKENS || 'WETH,USDT,DAI').split(','),
  enableAllTokenPairs: process.env.ENABLE_ALL_TOKEN_PAIRS === 'true',
  minTokenLiquidityUsd: parseFloat(process.env.MIN_TOKEN_LIQUIDITY_USD || '100000')
};

// Network-specific addresses
const MAINNET_ADDRESSES = {
  // Mainnet WETH
  WETH: '******************************************',
  // Mainnet tokens
  USDC: '******************************************', // USDC
  USDT: '******************************************', // USDT
  DAI: '******************************************', // DAI
  WBTC: '******************************************', // WBTC
  // Uniswap V2 on Mainnet
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************',
  // Aave V3 on Mainnet
  AAVE_POOL_ADDRESSES_PROVIDER: '******************************************',
  AAVE_POOL: '******************************************',
  // Additional DEXs for mainnet
  SUSHISWAP_ROUTER: '******************************************',
  PANCAKESWAP_ROUTER: '******************************************',
  BALANCER_VAULT: '******************************************',
  CURVE_REGISTRY: '******************************************'
};

const SEPOLIA_ADDRESSES = {
  // Sepolia WETH
  WETH: '******************************************',
  // Test tokens on Sepolia
  USDC: '******************************************',
  USDT: '******************************************',
  DAI: '******************************************',
  // Uniswap V2 on Sepolia
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************',
  // Aave V3 on Sepolia
  AAVE_POOL_ADDRESSES_PROVIDER: '******************************************',
  AAVE_POOL: '******************************************',
  // Additional DEXs for Sepolia (limited availability)
  SUSHISWAP_ROUTER: '', // Not available on Sepolia
  PANCAKESWAP_ROUTER: '', // Not available on Sepolia
  BALANCER_VAULT: '******************************************' // Same address on all networks
};

// Select addresses based on chain ID
export const ADDRESSES = config.chainId === 1 ? MAINNET_ADDRESSES : SEPOLIA_ADDRESSES;

// Comprehensive ERC-20 token definitions (Top tokens by market cap and trading volume)
const MAINNET_TOKENS = [
  // Stablecoins (Primary flashloan tokens)
  { address: MAINNET_ADDRESSES.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin', category: 'stablecoin', priority: 1 },
  { address: MAINNET_ADDRESSES.USDT, symbol: 'USDT', decimals: 6, name: 'Tether USD', category: 'stablecoin', priority: 2 },
  { address: MAINNET_ADDRESSES.DAI, symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin', category: 'stablecoin', priority: 3 },

  // Major cryptocurrencies
  { address: MAINNET_ADDRESSES.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether', category: 'major', priority: 1 },
  { address: MAINNET_ADDRESSES.WBTC, symbol: 'WBTC', decimals: 8, name: 'Wrapped Bitcoin', category: 'major', priority: 2 },

  // DeFi tokens (High liquidity)
  { address: '******************************************', symbol: 'UNI', decimals: 18, name: 'Uniswap', category: 'defi', priority: 1 },
  { address: '******************************************', symbol: 'LINK', decimals: 18, name: 'Chainlink', category: 'defi', priority: 2 },
  { address: '******************************************', symbol: 'AAVE', decimals: 18, name: 'Aave', category: 'defi', priority: 3 },
  { address: '******************************************', symbol: 'CRV', decimals: 18, name: 'Curve DAO Token', category: 'defi', priority: 4 },
  { address: '******************************************', symbol: 'COMP', decimals: 18, name: 'Compound', category: 'defi', priority: 5 },

  // Layer 2 tokens
  { address: '******************************************', symbol: 'OP', decimals: 18, name: 'Optimism', category: 'layer2', priority: 1 },
  { address: '0x7D1AfA7B718fb893dB30A3aBc0Cfc608AaCfeBB0', symbol: 'MATIC', decimals: 18, name: 'Polygon', category: 'layer2', priority: 2 },

  // Meme tokens (High volatility, good for arbitrage)
  { address: '0x95aD61b0a150d79219dCF64E1E6Cc01f0B64C4cE', symbol: 'SHIB', decimals: 18, name: 'Shiba Inu', category: 'meme', priority: 1 },
  { address: '0x4d224452801ACEd8B2F0aebE155379bb5D594381', symbol: 'APE', decimals: 18, name: 'ApeCoin', category: 'meme', priority: 2 },

  // Exchange tokens
  { address: '******************************************', symbol: 'BNB', decimals: 18, name: 'Binance Coin', category: 'exchange', priority: 1 },
  { address: '******************************************', symbol: 'FTT', decimals: 18, name: 'FTX Token', category: 'exchange', priority: 2 }
];

const SEPOLIA_TOKENS = [
  // Limited tokens for testnet to minimize rate limit issues
  { address: SEPOLIA_ADDRESSES.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether', category: 'major', priority: 1 },
  { address: SEPOLIA_ADDRESSES.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin', category: 'stablecoin', priority: 1 }
];

// Select tokens based on chain ID
export const COMMON_TOKENS = config.chainId === 1 ? MAINNET_TOKENS : SEPOLIA_TOKENS;

// DEX Configuration
export type DexName = 'UNISWAP_V2' | 'UNISWAP_V3' | 'SUSHISWAP' | 'PANCAKESWAP' | 'BALANCER';

export interface DexConfig {
  name: DexName;
  router: string;
  protocol: 'uniswap-v2' | 'uniswap-v3' | 'balancer';
  available: boolean;
  fees?: number[]; // Available fee tiers for V3-style DEXs
}

// Get DEX configuration based on current network
export function getDexConfig(): Record<DexName, DexConfig> {
  const isMainnet = config.chainId === 1;

  return {
    UNISWAP_V2: {
      name: 'UNISWAP_V2',
      router: ADDRESSES.UNISWAP_V2_ROUTER,
      protocol: 'uniswap-v2',
      available: true
    },
    UNISWAP_V3: {
      name: 'UNISWAP_V3',
      router: ADDRESSES.UNISWAP_V3_ROUTER,
      protocol: 'uniswap-v3',
      available: true,
      fees: [500, 3000, 10000] // 0.05%, 0.3%, 1%
    },
    SUSHISWAP: {
      name: 'SUSHISWAP',
      router: ADDRESSES.SUSHISWAP_ROUTER,
      protocol: 'uniswap-v2',
      available: isMainnet && ADDRESSES.SUSHISWAP_ROUTER !== ''
    },
    PANCAKESWAP: {
      name: 'PANCAKESWAP',
      router: ADDRESSES.PANCAKESWAP_ROUTER,
      protocol: 'uniswap-v2',
      available: isMainnet && ADDRESSES.PANCAKESWAP_ROUTER !== ''
    },
    BALANCER: {
      name: 'BALANCER',
      router: ADDRESSES.BALANCER_VAULT,
      protocol: 'balancer',
      available: ADDRESSES.BALANCER_VAULT !== ''
    }
  };
}

// Get available DEX pairs for flashloan arbitrage
export function getAvailableDexPairs(): DexName[] {
  const dexConfig = getDexConfig();
  return Object.values(dexConfig)
    .filter(dex => dex.available)
    .map(dex => dex.name);
}

// Token Configuration
export type TokenSymbol = 'WETH' | 'USDC' | 'USDT' | 'DAI' | 'WBTC' | 'UNI' | 'LINK' | 'AAVE' | 'CRV' | 'COMP' | 'OP' | 'MATIC' | 'SHIB' | 'APE' | 'BNB' | 'FTT';
export type TokenCategory = 'stablecoin' | 'major' | 'defi' | 'layer2' | 'meme' | 'exchange';

export interface TokenConfig {
  address: string;
  symbol: TokenSymbol;
  decimals: number;
  name: string;
  category: TokenCategory;
  priority: number;
}

// Get all available tokens for current network
export function getAllTokens(): TokenConfig[] {
  return config.chainId === 1 ? MAINNET_TOKENS : SEPOLIA_TOKENS;
}

// Get configured tokens for flashloan arbitrage
export function getConfiguredTokens(): TokenConfig[] {
  const allTokens = getAllTokens();
  const configuredSymbols = config.flashloanTokens || ['USDC', 'WETH'];

  return allTokens.filter(token =>
    configuredSymbols.includes(token.symbol)
  );
}

// Get primary flashloan token
export function getPrimaryFlashloanToken(): TokenConfig | null {
  const allTokens = getAllTokens();
  const primarySymbol = config.flashloanPrimaryToken || 'USDC';

  return allTokens.find(token => token.symbol === primarySymbol) || null;
}

// Get target tokens for arbitrage
export function getTargetTokens(): TokenConfig[] {
  const allTokens = getAllTokens();
  const targetSymbols = config.flashloanTargetTokens || ['WETH', 'USDT', 'DAI'];

  return allTokens.filter(token =>
    targetSymbols.includes(token.symbol)
  );
}

// Get tokens by category
export function getTokensByCategory(category: TokenCategory): TokenConfig[] {
  const allTokens = getAllTokens();
  return allTokens.filter(token => token.category === category);
}

// Get high-priority tokens (best for arbitrage)
export function getHighPriorityTokens(): TokenConfig[] {
  const allTokens = getAllTokens();
  return allTokens
    .filter(token => token.priority <= 2) // Top 2 in each category
    .sort((a, b) => a.priority - b.priority);
}

// Validate token configuration
export function validateTokenConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const allTokens = getAllTokens();
  const allSymbols = allTokens.map(t => t.symbol);

  // Check if primary token exists
  const primaryToken = config.flashloanPrimaryToken;
  if (primaryToken && !allSymbols.includes(primaryToken as TokenSymbol)) {
    errors.push(`Primary flashloan token '${primaryToken}' not available on current network`);
  }

  // Check if configured tokens exist
  const configuredTokens = config.flashloanTokens || [];
  for (const symbol of configuredTokens) {
    if (!allSymbols.includes(symbol as TokenSymbol)) {
      errors.push(`Configured token '${symbol}' not available on current network`);
    }
  }

  // Check if target tokens exist
  const targetTokens = config.flashloanTargetTokens || [];
  for (const symbol of targetTokens) {
    if (!allSymbols.includes(symbol as TokenSymbol)) {
      errors.push(`Target token '${symbol}' not available on current network`);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export function validateConfig(): void {
  if (config.privateKey === '0x0000000000000000000000000000000000000000000000000000000000000000') {
    console.warn('⚠️  Using default private key - please set PRIVATE_KEY in .env');
  }

  if (config.rpcUrl.includes('YOUR_ALCHEMY_KEY')) {
    console.warn('⚠️  Using default RPC URL - please set RPC_URL or ALCHEMY_API_KEY in .env');
  }

  if (config.dryRun) {
    console.log('🧪 Running in DRY RUN mode - no real transactions will be sent');
  }

  const networkName = config.chainId === 11155111 ? 'Sepolia Testnet' :
                     config.chainId === 1 ? 'Ethereum Mainnet' :
                     `Chain ${config.chainId}`;

  // Validate token configuration
  const tokenValidation = validateTokenConfig();
  if (!tokenValidation.valid) {
    console.warn('⚠️  Token configuration issues:');
    tokenValidation.errors.forEach(error => console.warn(`   • ${error}`));
  }

  // Get configured tokens info
  const configuredTokens = getConfiguredTokens();
  const primaryToken = getPrimaryFlashloanToken();
  const targetTokens = getTargetTokens();

  console.log(`🔧 Configuration loaded:
    - Network: ${networkName}
    - Chain ID: ${config.chainId}
    - Min Profit: ${config.minProfitWei} wei
    - Max Gas Price: ${config.maxGasPriceGwei} gwei
    - Slippage Tolerance: ${config.slippageTolerance * 100}%
    - Strategies: ${[
      config.enableSandwichAttacks && 'Sandwich',
      config.enableFrontRunning && 'Front-running',
      config.enableArbitrage && 'Arbitrage',
      config.enableFlashloanAttacks && 'Flashloan'
    ].filter(Boolean).join(', ')}
    - Flashloan Tokens: ${configuredTokens.map(t => t.symbol).join(', ')}
    - Primary Token: ${primaryToken?.symbol || 'None'}
    - Target Tokens: ${targetTokens.map(t => t.symbol).join(', ')}
    - DEX Pairs: ${config.flashloanDexPairs.join(', ')}
  `);

  if (config.chainId === 11155111) {
    console.log('🧪 Running on Sepolia testnet - perfect for safe testing!');
  }

  if (config.enableFlashloanAttacks && configuredTokens.length === 0) {
    console.warn('⚠️  Flashloan attacks enabled but no tokens configured!');
  }
}
