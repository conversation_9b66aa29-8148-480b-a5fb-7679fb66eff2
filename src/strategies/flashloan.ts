import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute, Pool, Token, Transaction } from '../types';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { CalldataEncoder } from '../calldata/encoder';
import { BundleSimulator } from '../simulation/simulator';
import { FlashloanContractInterface } from '../contracts/flashloan';
import { BalancerFlashloanStrategy } from './balancer-flashloan';
import { config, COMMON_TOKENS, ADDRESSES } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';

export class FlashloanStrategy {
  private poolManager: PoolManager;
  private gasOptimizer: GasOptimizer;
  private encoder: CalldataEncoder;
  private simulator: BundleSimulator;
  private flashloanInterface: FlashloanContractInterface;
  private balancerStrategy: BalancerFlashloanStrategy;
  private hybridContract: ethers.Contract | null = null;
  private wallet: ethers.Wallet;
  private provider: ethers.Provider;
  private readonly MIN_PROFIT_THRESHOLD: number;
  private readonly FLASHLOAN_PREMIUM_BPS = 9; // 0.09% premium
  private readonly MAX_FLASHLOAN_AMOUNT: bigint;

  constructor(provider: ethers.Provider) {
    this.provider = provider;
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
    this.encoder = new CalldataEncoder();
    this.simulator = new BundleSimulator();
    this.wallet = new ethers.Wallet(config.privateKey, provider);

    // Network-specific configuration
    const isMainnet = config.chainId === 1;
    this.MIN_PROFIT_THRESHOLD = isMainnet ? 0.005 : 0.02; // 0.5% mainnet, 2% testnet
    this.MAX_FLASHLOAN_AMOUNT = isMainnet
      ? ethers.parseUnits('100000', 6) // 100k USDC on mainnet
      : ethers.parseUnits('10000', 6);  // 10k USDC on testnet

    this.flashloanInterface = new FlashloanContractInterface(
      provider,
      ADDRESSES.AAVE_POOL,
      ADDRESSES.AAVE_POOL_ADDRESSES_PROVIDER
    );

    // Initialize Balancer strategy for 0% fee flashloans
    this.balancerStrategy = new BalancerFlashloanStrategy(provider);

    if (isMainnet) {
      enhancedLogger.systemStatus('🚨 MAINNET MODE: Using conservative flashloan parameters');
      enhancedLogger.systemStatus('🔄 HYBRID STRATEGY: Aave + Balancer flashloans');
      enhancedLogger.systemStatus(`   Min Profit: ${this.MIN_PROFIT_THRESHOLD * 100}%`);
      enhancedLogger.systemStatus(`   Max Amount: ${ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, 6)} USDC`);
      enhancedLogger.systemStatus('   💰 Balancer: 0% fees | Aave: 0.09% fees');
    }
  }

  async scanForFlashloanOpportunities(): Promise<FlashloanRoute[]> {
    const opportunities: FlashloanRoute[] = [];

    try {
      enhancedLogger.systemStatus('🔍 Scanning for hybrid flashloan opportunities...');
      enhancedLogger.systemStatus('   🔵 Balancer (0% fees) + 🟠 Aave (0.09% fees)');

      // Focus on USDC flashloans for arbitrage
      const flashloanToken = COMMON_TOKENS.find(t => t.symbol === 'USDC');
      if (!flashloanToken) {
        logger.warn('USDC token not found in COMMON_TOKENS');
        return opportunities;
      }

      // Scan for arbitrage opportunities between different DEXs
      for (let i = 0; i < COMMON_TOKENS.length; i++) {
        const targetToken = COMMON_TOKENS[i];
        
        if (targetToken.address === flashloanToken.address) {
          continue; // Skip same token
        }

        // Find arbitrage between Uniswap V2 and V3
        const arbitrageRoute = await this.findArbitrageOpportunity(flashloanToken, targetToken);
        
        if (arbitrageRoute) {
          // Calculate optimal flashloan amount
          const flashloanAmount = await this.calculateOptimalFlashloanAmount(
            flashloanToken,
            arbitrageRoute
          );

          if (flashloanAmount > BigInt(0)) {
            const flashloanRoute = await this.buildFlashloanRoute(
              flashloanToken,
              flashloanAmount,
              arbitrageRoute
            );

            if (flashloanRoute && flashloanRoute.confidence >= 70) {
              opportunities.push(flashloanRoute);
              enhancedLogger.profitCalculation(
                ethers.formatEther(flashloanRoute.expectedProfit),
                true
              );
            }
          }
        }
      }

      // Also scan Balancer opportunities (0% fees!)
      const balancerOpportunities = await this.balancerStrategy.scanForBalancerFlashloanOpportunities();
      opportunities.push(...balancerOpportunities);

      // Sort by expected profit
      opportunities.sort((a, b) =>
        Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
      );

      const aaveCount = opportunities.length - balancerOpportunities.length;
      const balancerCount = balancerOpportunities.length;

      enhancedLogger.systemStatus(`Found ${opportunities.length} total flashloan opportunities:`);
      enhancedLogger.systemStatus(`   🟠 Aave: ${aaveCount} opportunities (0.09% fees)`);
      enhancedLogger.systemStatus(`   🔵 Balancer: ${balancerCount} opportunities (0% fees)`);

      return opportunities.slice(0, 8); // Return top 8 (mix of both)

    } catch (error) {
      logger.logError(error as Error, 'FlashloanStrategy.scanForFlashloanOpportunities');
      return [];
    }
  }

  private async findArbitrageOpportunity(
    tokenA: Token,
    tokenB: Token
  ): Promise<ArbitrageRoute | null> {
    try {
      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v3', 3000);

      if (!v2Pool || !v3Pool) {
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, tokenA, tokenB);
      const v3Price = this.calculatePoolPrice(v3Pool, tokenA, tokenB);

      if (!v2Price || !v3Price) {
        return null;
      }

      // Check for price difference
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      if (priceDifference < this.MIN_PROFIT_THRESHOLD) {
        return null;
      }

      // Determine direction (buy low, sell high)
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;

      // Calculate optimal amount for arbitrage
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, tokenA, tokenB);

      if (optimalAmount === BigInt(0)) {
        return null;
      }

      // Estimate gas costs
      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount);

      // Calculate expected profit
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, tokenA, tokenB, optimalAmount, gasEstimate
      );

      if (BigInt(expectedProfit.toString()) <= BigInt(0)) {
        return null;
      }

      return {
        pools: [buyPool, sellPool],
        tokens: [tokenA, tokenB],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };

    } catch (error) {
      logger.debug('Error finding arbitrage opportunity', { error: (error as Error).message });
      return null;
    }
  }

  private async calculateOptimalFlashloanAmount(
    flashloanToken: Token,
    arbitrageRoute: ArbitrageRoute
  ): Promise<bigint> {
    try {
      // Start with a base amount and optimize
      const baseAmount = ethers.parseUnits('1000', flashloanToken.decimals); // 1000 USDC
      let optimalAmount = BigInt(0);
      let maxNetProfit = BigInt(0);

      // Test different flashloan amounts
      for (let multiplier = 1; multiplier <= 10; multiplier++) {
        const testAmount = (baseAmount * BigInt(multiplier)) / BigInt(1);
        
        // Calculate flashloan premium
        const premium = await this.calculateFlashloanPremium(testAmount);
        
        // Estimate arbitrage profit with this amount
        const arbitrageProfit = await this.estimateArbitrageProfit(arbitrageRoute, testAmount);
        
        // Calculate net profit (arbitrage profit - flashloan premium - gas)
        const netProfit = arbitrageProfit - premium - BigInt(arbitrageRoute.gasEstimate.toString());
        
        if (netProfit > maxNetProfit) {
          maxNetProfit = netProfit;
          optimalAmount = testAmount;
        }
      }

      return optimalAmount;
    } catch (error) {
      logger.debug('Error calculating optimal flashloan amount', { error: (error as Error).message });
      return BigInt(0);
    }
  }

  private async buildFlashloanRoute(
    flashloanToken: Token,
    flashloanAmount: bigint,
    arbitrageRoute: ArbitrageRoute
  ): Promise<FlashloanRoute | null> {
    try {
      // Calculate flashloan premium
      const flashloanPremium = await this.calculateFlashloanPremium(flashloanAmount);
      
      // Estimate total gas cost (flashloan + arbitrage)
      const flashloanGas = await this.estimateFlashloanGasCost();
      const totalGasEstimate = BigInt(arbitrageRoute.gasEstimate.toString()) + flashloanGas;
      
      // Calculate expected profit
      const arbitrageProfit = await this.estimateArbitrageProfit(arbitrageRoute, flashloanAmount);
      const expectedProfit = arbitrageProfit - flashloanPremium - totalGasEstimate;
      
      if (expectedProfit <= BigInt(0)) {
        return null;
      }

      // Calculate confidence based on profit margin
      const profitMargin = Number(expectedProfit * BigInt(10000) / flashloanAmount) / 100;
      const confidence = this.calculateFlashloanConfidence(profitMargin, expectedProfit);

      return {
        flashloanToken,
        flashloanAmount,
        flashloanPremium,
        arbitrageRoute,
        expectedProfit,
        gasEstimate: totalGasEstimate,
        confidence
      };

    } catch (error) {
      logger.debug('Error building flashloan route', { error: (error as Error).message });
      return null;
    }
  }

  private async calculateFlashloanPremium(amount: bigint): Promise<bigint> {
    try {
      const premiumBps = await this.flashloanInterface.getFlashloanPremium();
      return (amount * premiumBps) / BigInt(10000);
    } catch (error) {
      // Fallback to default premium
      return (amount * BigInt(this.FLASHLOAN_PREMIUM_BPS)) / BigInt(10000);
    }
  }

  private calculatePoolPrice(pool: Pool, token0: Token, token1: Token): number | null {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const reserve0 = Number(ethers.formatUnits(pool.reserves.reserve0, token0.decimals));
      const reserve1 = Number(ethers.formatUnits(pool.reserves.reserve1, token1.decimals));
      return reserve1 / reserve0; // Price of token0 in terms of token1
    } else if (pool.protocol === 'uniswap-v3' && pool.sqrtPriceX96) {
      // Convert sqrtPriceX96 to actual price
      const sqrtPrice = Number(pool.sqrtPriceX96) / (2 ** 96);
      const price = sqrtPrice ** 2;

      // Adjust for token decimals
      const decimalsAdjustment = 10 ** (token1.decimals - token0.decimals);
      return price * decimalsAdjustment;
    }

    return null;
  }

  private async calculateOptimalArbitrageAmount(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token
  ): Promise<bigint> {
    // Simplified calculation - in practice would use more sophisticated optimization
    const maxAmount = ethers.parseUnits('5000', buyToken.decimals); // Max 5000 tokens
    return maxAmount / BigInt(2); // Use half as starting point
  }

  private async estimateArbitrageGasCost(
    buyPool: Pool,
    sellPool: Pool,
    amount: bigint
  ): Promise<bigint> {
    // Estimate gas for two swaps within flashloan
    const gasPerSwap = BigInt(150000); // Approximate gas per swap
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return gasPerSwap * BigInt(2) * BigInt(gasStrategy.maxFeePerGas.toString());
  }

  private async calculateArbitrageProfit(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token,
    amount: bigint,
    gasCost: bigint
  ): Promise<bigint> {
    // Simplified profit calculation
    // In practice, would simulate actual swaps
    const estimatedReturn = (amount * BigInt(102)) / BigInt(100); // 2% profit estimate
    return estimatedReturn - amount - gasCost;
  }

  private calculateArbitrageConfidence(profitPercentage: number, expectedProfit: bigint): number {
    let confidence = 0;

    // Profit percentage factor
    confidence += Math.min(profitPercentage * 15, 40); // Max 40 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 20, 25); // Max 25 points

    // Arbitrage base confidence
    confidence += 15;

    return Math.min(confidence, 100);
  }

  private async estimateArbitrageProfit(arbitrageRoute: ArbitrageRoute, amount: bigint): Promise<bigint> {
    // Simplified estimation - would use actual pool calculations
    return (amount * BigInt(102)) / BigInt(100) - amount; // 2% profit estimate
  }

  private async estimateFlashloanGasCost(): Promise<bigint> {
    // Estimate gas for flashloan execution
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(200000) * BigInt(gasStrategy.maxFeePerGas.toString()); // ~200k gas for flashloan
  }

  private calculateFlashloanConfidence(profitMargin: number, expectedProfit: bigint): number {
    let confidence = 0;

    // Profit margin factor (higher threshold for flashloans)
    confidence += Math.min(profitMargin * 10, 35); // Max 35 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 15, 25); // Max 25 points

    // Flashloan complexity penalty
    confidence += 10; // Base confidence for flashloan

    return Math.min(confidence, 100);
  }

  async executeFlashloan(route: FlashloanRoute): Promise<boolean> {
    try {
      enhancedLogger.separator();
      enhancedLogger.systemStatus('🚀 Executing Flashloan Arbitrage Attack');
      enhancedLogger.systemStatus(`Flashloan Amount: ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
      enhancedLogger.profitCalculation(ethers.formatEther(route.expectedProfit), true);
      enhancedLogger.systemStatus(`Confidence: ${route.confidence}%`);

      if (config.dryRun) {
        enhancedLogger.systemStatus('DRY RUN: Simulating flashloan arbitrage execution...');

        // Simulate the flashloan execution steps
        enhancedLogger.systemStatus('Step 1: 💰 Flashloan USDC from Aave');
        enhancedLogger.systemStatus(`  └─ Borrowing ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} USDC`);
        enhancedLogger.systemStatus(`  └─ Premium: ${ethers.formatUnits(route.flashloanPremium, route.flashloanToken.decimals)} USDC`);

        enhancedLogger.systemStatus('Step 2: 🔄 Execute Arbitrage');
        enhancedLogger.systemStatus(`  └─ Buy on ${route.arbitrageRoute.pools[0].protocol.toUpperCase()}`);
        enhancedLogger.systemStatus(`  └─ Sell on ${route.arbitrageRoute.pools[1].protocol.toUpperCase()}`);

        enhancedLogger.systemStatus('Step 3: 💸 Repay Flashloan');
        const totalRepayment = BigInt(route.flashloanAmount.toString()) + BigInt(route.flashloanPremium.toString());
        enhancedLogger.systemStatus(`  └─ Repaying ${ethers.formatUnits(totalRepayment, route.flashloanToken.decimals)} USDC`);

        enhancedLogger.systemStatus('Step 4: 💎 Keep Profit');
        enhancedLogger.profitCalculation(ethers.formatEther(route.expectedProfit), true);

        enhancedLogger.success('✅ Flashloan arbitrage simulation completed successfully');
        enhancedLogger.separator();
        return true;
      }

      // Create flashloan transaction
      const flashloanTx = await this.createFlashloanTransaction(route);

      if (!flashloanTx) {
        enhancedLogger.error('Failed to create flashloan transaction');
        return false;
      }

      // Simulate the transaction bundle
      const simulationResult = await this.simulator.simulateBundle({
        transactions: [flashloanTx],
        blockNumber: await this.provider.getBlockNumber() + 1
      });

      if (!simulationResult.success) {
        enhancedLogger.error('Flashloan simulation failed', simulationResult.error);
        return false;
      }

      enhancedLogger.success('✅ Flashloan arbitrage executed successfully');
      enhancedLogger.separator();
      return true;

    } catch (error) {
      enhancedLogger.error('Flashloan execution failed', error);
      logger.logError(error as Error, 'FlashloanStrategy.executeFlashloan');
      return false;
    }
  }

  private async createFlashloanTransaction(route: FlashloanRoute): Promise<Transaction | null> {
    try {
      // Encode the arbitrage parameters for the flashloan callback
      const arbitrageData = {
        routers: [
          route.arbitrageRoute.pools[0].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER,
          route.arbitrageRoute.pools[1].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER
        ],
        amounts: [route.flashloanAmount, route.flashloanAmount],
        swapData: [
          this.encoder.encodeArbitrageSwap(
            route.arbitrageRoute.tokens[0],
            route.arbitrageRoute.tokens[1],
            route.flashloanAmount,
            route.arbitrageRoute.pools[0].protocol as 'uniswap-v2' | 'uniswap-v3',
            this.wallet.address,
            route.arbitrageRoute.pools[0].fee
          ),
          this.encoder.encodeArbitrageSwap(
            route.arbitrageRoute.tokens[1],
            route.arbitrageRoute.tokens[0],
            route.flashloanAmount,
            route.arbitrageRoute.pools[1].protocol as 'uniswap-v2' | 'uniswap-v3',
            this.wallet.address,
            route.arbitrageRoute.pools[1].fee
          )
        ]
      };

      const params = this.flashloanInterface.encodeFlashloanParams(arbitrageData);

      // Create flashloan call data
      const flashloanCalldata = new ethers.Interface([
        'function flashLoan(address[] assets, uint256[] amounts, uint256[] interestRateModes, address onBehalfOf, bytes params, uint16 referralCode)'
      ]).encodeFunctionData('flashLoan', [
        [route.flashloanToken.address],
        [route.flashloanAmount],
        [0], // Variable interest rate mode
        this.wallet.address,
        params,
        0 // No referral
      ]);

      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      const nonce = await this.wallet.getNonce();

      return {
        hash: '',
        from: this.wallet.address,
        to: ADDRESSES.AAVE_POOL,
        value: BigInt(0),
        gasPrice: gasStrategy.maxFeePerGas,
        gasLimit: route.gasEstimate,
        data: flashloanCalldata,
        nonce,
        maxFeePerGas: gasStrategy.maxFeePerGas,
        maxPriorityFeePerGas: gasStrategy.priorityFee
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashloanStrategy.createFlashloanTransaction');
      return null;
    }
  }
}
