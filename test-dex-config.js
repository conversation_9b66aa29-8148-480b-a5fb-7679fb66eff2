#!/usr/bin/env node

/**
 * Test script to verify DEX configuration for flashloan arbitrage
 */

const { config, getDexConfig, getAvailableDexPairs, getAllTokens, getConfiguredTokens, getPrimaryFlashloanToken, getTargetTokens, validateTokenConfig } = require('./dist/config');

console.log('🔧 Testing Curve + Uniswap V3 Strategy Configuration\n');

// Test 1: Basic configuration loading
console.log('📋 Current Configuration:');
console.log(`   Chain ID: ${config.chainId}`);
console.log(`   Network: ${config.chainId === 1 ? 'Mainnet' : 'Sepolia'}`);
console.log(`   Flashloan DEX Pairs: ${config.flashloanDexPairs.join(', ')}`);
console.log(`   Buy DEX: ${config.flashloanBuyDex}`);
console.log(`   Sell DEX: ${config.flashloanSellDex}`);
console.log(`   Cross-DEX Arbitrage: ${config.enableCrossDexArbitrage ? 'Enabled' : 'Disabled'}`);
console.log(`   Min Arbitrage Spread: ${config.minArbitrageSpread}%`);
console.log(`   Flashloan Tokens: ${config.flashloanTokens.join(', ')}`);
console.log(`   Primary Token: ${config.flashloanPrimaryToken}`);
console.log(`   Target Tokens: ${config.flashloanTargetTokens.join(', ')}`);
console.log(`   All Token Pairs: ${config.enableAllTokenPairs ? 'Enabled' : 'Disabled'}`);
console.log(`   Min Token Liquidity: $${config.minTokenLiquidityUsd.toLocaleString()}\n`);

// Test 2: Available DEXs
console.log('🔍 Available DEXs on Current Network:');
const availableDexs = getAvailableDexPairs();
console.log(`   Available: ${availableDexs.join(', ')}\n`);

// Test 2.5: Available Tokens
console.log('💰 Available Tokens on Current Network:');
const allTokens = getAllTokens();
console.log(`   Total Available: ${allTokens.length} tokens`);
allTokens.forEach(token => {
  console.log(`   • ${token.symbol} (${token.name}) - ${token.category} - Priority: ${token.priority}`);
});
console.log('');

// Test 3: DEX Configuration Details
console.log('⚙️  DEX Configuration Details:');
const dexConfig = getDexConfig();
Object.entries(dexConfig).forEach(([name, config]) => {
  const status = config.available ? '✅' : '❌';
  console.log(`   ${status} ${name}:`);
  console.log(`      Protocol: ${config.protocol}`);
  console.log(`      Router: ${config.router || 'N/A'}`);
  if (config.fees) {
    console.log(`      Fee Tiers: ${config.fees.join(', ')} bps`);
  }
  console.log('');
});

// Test 4: Token Configuration
console.log('🎯 Token Configuration:');
const configuredTokens = getConfiguredTokens();
const primaryToken = getPrimaryFlashloanToken();
const targetTokens = getTargetTokens();

console.log(`   Configured Tokens: ${configuredTokens.map(t => t.symbol).join(', ')}`);
console.log(`   Primary Token: ${primaryToken ? primaryToken.symbol : 'None'}`);
console.log(`   Target Tokens: ${targetTokens.map(t => t.symbol).join(', ')}\n`);

// Test 5: Validate Configuration
console.log('✅ Configuration Validation:');

// Validate token configuration
const tokenValidation = validateTokenConfig();
if (tokenValidation.valid) {
  console.log('   ✅ Token configuration is valid');
} else {
  console.log('   ❌ Token configuration errors:');
  tokenValidation.errors.forEach(error => console.log(`      • ${error}`));
}

// Check if configured DEXs are available
const configuredDexs = config.flashloanDexPairs.filter(dex => 
  availableDexs.includes(dex)
);

if (configuredDexs.length >= 2) {
  console.log(`   ✅ Valid: ${configuredDexs.length} DEXs available for arbitrage`);
  console.log(`   📊 Configured DEXs: ${configuredDexs.join(', ')}`);
} else {
  console.log(`   ❌ Error: Need at least 2 available DEXs, found ${configuredDexs.length}`);
  console.log(`   💡 Available options: ${availableDexs.join(', ')}`);
}

// Check buy/sell DEX availability
const buyDexAvailable = availableDexs.includes(config.flashloanBuyDex);
const sellDexAvailable = availableDexs.includes(config.flashloanSellDex);

console.log(`   Buy DEX (${config.flashloanBuyDex}): ${buyDexAvailable ? '✅' : '❌'}`);
console.log(`   Sell DEX (${config.flashloanSellDex}): ${sellDexAvailable ? '✅' : '❌'}`);

if (!config.enableCrossDexArbitrage && (!buyDexAvailable || !sellDexAvailable)) {
  console.log('   ⚠️  Warning: Specific DEX mode requires both buy and sell DEXs to be available');
}

console.log('\n🎯 Recommendations:');

if (config.chainId === 11155111) {
  // Sepolia recommendations
  console.log('   📍 Sepolia Testnet:');
  console.log('   • Use UNISWAP_V2,UNISWAP_V3 for DEX pairs');
  console.log('   • Enable cross-DEX arbitrage for best opportunities');
  console.log('   • Set minimum spread to 0.5% for testing');
} else {
  // Mainnet recommendations
  console.log('   📍 Ethereum Mainnet:');
  console.log('   • Use UNISWAP_V2,UNISWAP_V3,SUSHISWAP,PANCAKESWAP for maximum opportunities');
  console.log('   • Enable cross-DEX arbitrage for automatic optimization');
  console.log('   • Set minimum spread to 0.3% for competitive execution');
}

console.log('\n🚀 Strategy Configurations:');

console.log('\n   🎯 Current: Curve + Uniswap V3 Stablecoin Strategy');
console.log('   FLASHLOAN_DEX_PAIRS=CURVE,UNISWAP_V3');
console.log('   FLASHLOAN_TOKENS=USDC,DAI,USDT');
console.log('   FLASHLOAN_PRIMARY_TOKEN=USDC');
console.log('   FLASHLOAN_TARGET_TOKENS=DAI,USDT');
console.log('   ENABLE_ALL_TOKEN_PAIRS=true');
console.log('   MIN_ARBITRAGE_SPREAD=0.1');

console.log('\n   📊 Strategy Benefits:');
console.log('   • Ultra-low slippage on Curve (0.04% fees)');
console.log('   • High liquidity on Uniswap V3');
console.log('   • Stablecoin arbitrage opportunities');
console.log('   • Consistent profit margins (0.1-2%)');

console.log('\n   ⚠️  Network Requirements:');
console.log('   • Mainnet only (Curve 3pool not on Sepolia)');
console.log('   • High liquidity thresholds ($500k+)');
console.log('   • Optimized for stablecoin pairs');

console.log('\n   🔄 Alternative Configurations:');
console.log('   Conservative: MIN_ARBITRAGE_SPREAD=0.2');
console.log('   Aggressive: MIN_ARBITRAGE_SPREAD=0.05');

console.log('\n✨ Configuration test completed!');
