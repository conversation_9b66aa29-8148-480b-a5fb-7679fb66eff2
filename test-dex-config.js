#!/usr/bin/env node

/**
 * Test script to verify DEX configuration for flashloan arbitrage
 */

const { config, getDexConfig, getAvailableDexPairs } = require('./dist/config');

console.log('🔧 Testing DEX Configuration for Flashloan Arbitrage\n');

// Test 1: Basic configuration loading
console.log('📋 Current Configuration:');
console.log(`   Chain ID: ${config.chainId}`);
console.log(`   Network: ${config.chainId === 1 ? 'Mainnet' : 'Sepolia'}`);
console.log(`   Flashloan DEX Pairs: ${config.flashloanDexPairs.join(', ')}`);
console.log(`   Buy DEX: ${config.flashloanBuyDex}`);
console.log(`   Sell DEX: ${config.flashloanSellDex}`);
console.log(`   Cross-DEX Arbitrage: ${config.enableCrossDexArbitrage ? 'Enabled' : 'Disabled'}`);
console.log(`   Min Arbitrage Spread: ${config.minArbitrageSpread}%\n`);

// Test 2: Available DEXs
console.log('🔍 Available DEXs on Current Network:');
const availableDexs = getAvailableDexPairs();
console.log(`   Available: ${availableDexs.join(', ')}\n`);

// Test 3: DEX Configuration Details
console.log('⚙️  DEX Configuration Details:');
const dexConfig = getDexConfig();
Object.entries(dexConfig).forEach(([name, config]) => {
  const status = config.available ? '✅' : '❌';
  console.log(`   ${status} ${name}:`);
  console.log(`      Protocol: ${config.protocol}`);
  console.log(`      Router: ${config.router || 'N/A'}`);
  if (config.fees) {
    console.log(`      Fee Tiers: ${config.fees.join(', ')} bps`);
  }
  console.log('');
});

// Test 4: Validate Configuration
console.log('✅ Configuration Validation:');

// Check if configured DEXs are available
const configuredDexs = config.flashloanDexPairs.filter(dex => 
  availableDexs.includes(dex)
);

if (configuredDexs.length >= 2) {
  console.log(`   ✅ Valid: ${configuredDexs.length} DEXs available for arbitrage`);
  console.log(`   📊 Configured DEXs: ${configuredDexs.join(', ')}`);
} else {
  console.log(`   ❌ Error: Need at least 2 available DEXs, found ${configuredDexs.length}`);
  console.log(`   💡 Available options: ${availableDexs.join(', ')}`);
}

// Check buy/sell DEX availability
const buyDexAvailable = availableDexs.includes(config.flashloanBuyDex);
const sellDexAvailable = availableDexs.includes(config.flashloanSellDex);

console.log(`   Buy DEX (${config.flashloanBuyDex}): ${buyDexAvailable ? '✅' : '❌'}`);
console.log(`   Sell DEX (${config.flashloanSellDex}): ${sellDexAvailable ? '✅' : '❌'}`);

if (!config.enableCrossDexArbitrage && (!buyDexAvailable || !sellDexAvailable)) {
  console.log('   ⚠️  Warning: Specific DEX mode requires both buy and sell DEXs to be available');
}

console.log('\n🎯 Recommendations:');

if (config.chainId === 11155111) {
  // Sepolia recommendations
  console.log('   📍 Sepolia Testnet:');
  console.log('   • Use UNISWAP_V2,UNISWAP_V3 for DEX pairs');
  console.log('   • Enable cross-DEX arbitrage for best opportunities');
  console.log('   • Set minimum spread to 0.5% for testing');
} else {
  // Mainnet recommendations
  console.log('   📍 Ethereum Mainnet:');
  console.log('   • Use UNISWAP_V2,UNISWAP_V3,SUSHISWAP,PANCAKESWAP for maximum opportunities');
  console.log('   • Enable cross-DEX arbitrage for automatic optimization');
  console.log('   • Set minimum spread to 0.3% for competitive execution');
}

console.log('\n🚀 Example Configurations:');

console.log('\n   Sepolia Testing:');
console.log('   FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3');
console.log('   ENABLE_CROSS_DEX_ARBITRAGE=true');
console.log('   MIN_ARBITRAGE_SPREAD=0.5');

console.log('\n   Mainnet Production:');
console.log('   FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3,SUSHISWAP,PANCAKESWAP');
console.log('   ENABLE_CROSS_DEX_ARBITRAGE=true');
console.log('   MIN_ARBITRAGE_SPREAD=0.3');

console.log('\n   Targeted Strategy:');
console.log('   FLASHLOAN_DEX_PAIRS=UNISWAP_V2,SUSHISWAP');
console.log('   FLASHLOAN_BUY_DEX=SUSHISWAP');
console.log('   FLASHLOAN_SELL_DEX=UNISWAP_V2');
console.log('   ENABLE_CROSS_DEX_ARBITRAGE=false');

console.log('\n✨ Configuration test completed!');
